import MobileBanner from "./components/mobile-banner"

export default function MobileBannerDemo() {
  // 自定义轮播数据
  const bannerItems = [
    {
      id: 1,
      image: "/images/banner1.png",
      alt: "Toxic Romance - 动漫风格横幅",
    },
    {
      id: 2,
      image: "/placeholder.svg?height=200&width=350",
      alt: "横幅 2",
    },
    {
      id: 3,
      image: "/placeholder.svg?height=200&width=350",
      alt: "横幅 3",
    },
    {
      id: 4,
      image: "/placeholder.svg?height=200&width=350",
      alt: "横幅 4",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        <h1 className="text-2xl font-bold text-center text-gray-800">手机端轮播横幅组件</h1>

        {/* 默认配置的横幅 */}
        <div>
          <h2 className="text-lg font-semibold mb-3 text-gray-700">默认横幅</h2>
          <MobileBanner />
        </div>

        {/* 自定义高度的横幅 */}
        <div>
          <h2 className="text-lg font-semibold mb-3 text-gray-700">自定义高度 (150px)</h2>
          <MobileBanner items={bannerItems} height={150} autoPlayInterval={4000} />
        </div>

        {/* 关闭自动播放的横幅 */}
        <div>
          <h2 className="text-lg font-semibold mb-3 text-gray-700">手动控制</h2>
          <MobileBanner items={bannerItems} autoPlay={false} height={180} />
        </div>

        {/* 使用说明 */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold mb-2 text-gray-800">使用说明：</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 在手机上可以左右滑动切换</li>
            <li>• 点击底部指示器快速跳转</li>
            <li>• 支持自动播放功能</li>
            <li>• 图片会自动缩放适应容器</li>
            <li>• 桌面端显示左右箭头按钮</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
