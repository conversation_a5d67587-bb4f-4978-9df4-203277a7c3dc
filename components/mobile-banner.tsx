"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"

interface BannerItem {
  id: number
  image: string
  alt: string
}

interface MobileBannerProps {
  items?: BannerItem[]
  autoPlay?: boolean
  autoPlayInterval?: number
  height?: number
}

const defaultItems: BannerItem[] = [
  {
    id: 1,
    image: "/images/banner1.png",
    alt: "Toxic Romance Banner",
  },
  {
    id: 2,
    image: "/placeholder.svg?height=200&width=350",
    alt: "Banner 2",
  },
  {
    id: 3,
    image: "/placeholder.svg?height=200&width=350",
    alt: "Banner 3",
  },
  {
    id: 4,
    image: "/placeholder.svg?height=200&width=350",
    alt: "Banner 4",
  },
]

export default function MobileBanner({
  items = defaultItems,
  autoPlay = true,
  autoPlayInterval = 3000,
  height = 200,
}: MobileBannerProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)
  const touchStartX = useRef<number>(0)
  const touchEndX = useRef<number>(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动播放
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex === items.length - 1 ? 0 : prevIndex + 1))
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [isAutoPlaying, items.length, autoPlayInterval])

  // 处理触摸开始
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX
    setIsAutoPlaying(false)
  }

  // 处理触摸移动
  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX
  }

  // 处理触摸结束
  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return

    const distance = touchStartX.current - touchEndX.current
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }

    // 重新启动自动播放
    setTimeout(() => setIsAutoPlaying(autoPlay), 2000)
  }

  // 下一张
  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === items.length - 1 ? 0 : prevIndex + 1))
  }

  // 上一张
  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? items.length - 1 : prevIndex - 1))
  }

  // 跳转到指定索引
  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(autoPlay), 2000)
  }

  return (
    <div className="relative w-full mx-auto bg-gray-100 rounded-lg overflow-hidden shadow-lg">
      {/* 轮播容器 */}
      <div
        ref={containerRef}
        className="relative overflow-hidden"
        style={{ height: `${height}px` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* 轮播内容 */}
        <div
          className="flex transition-transform duration-300 ease-in-out h-full"
          style={{
            transform: `translateX(-${currentIndex * 100}%)`,
            width: `${items.length * 100}%`,
          }}
        >
          {items.map((item) => (
            <div
              key={item.id}
              className="relative flex-shrink-0 w-full h-full"
              style={{ width: `${100 / items.length}%` }}
            >
              <Image
                src={item.image || "/placeholder.svg"}
                alt={item.alt}
                fill
                className="object-cover"
                sizes="100vw"
                priority={item.id === 1}
              />
            </div>
          ))}
        </div>
      </div>

      {/* 底部指示器 */}
      <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex space-x-2">
        {items.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-200 ${
              index === currentIndex ? "bg-white scale-125" : "bg-white/50 hover:bg-white/75"
            }`}
            aria-label={`跳转到第${index + 1}张`}
          />
        ))}
      </div>
    </div>
  )
}
